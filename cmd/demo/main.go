package main

import (
	"fmt"
	"log"
	"net/http"
	"time"

	"go-api-solve/internal/middleware"
	"go-api-solve/internal/model"
	"go-api-solve/internal/utils"

	"github.com/gin-gonic/gin"
)

// 演示版本的API服务，不需要数据库连接
func main() {
	fmt.Println("=== Go API Solve 演示版本 ===")
	fmt.Println("这是一个演示版本，模拟完整的API功能")
	fmt.Println("不需要数据库和Redis连接")
	
	// 设置Gin模式
	gin.SetMode(gin.DebugMode)

	// 创建Gin路由器
	router := gin.New()

	// 添加中间件
	router.Use(middleware.Logger())
	router.Use(middleware.Recovery())
	router.Use(middleware.CORS())

	// 注册路由
	setupDemoRoutes(router)

	// 启动服务器
	port := "8080"
	fmt.Printf("\n🚀 演示服务器启动在端口 %s\n", port)
	fmt.Printf("📖 访问以下URL进行测试:\n")
	fmt.Printf("   健康检查: http://localhost:%s/api/v1/health\n", port)
	fmt.Printf("   处理图片: http://localhost:%s/api/v1/process-image\n", port)
	fmt.Printf("   项目信息: http://localhost:%s/api/v1/info\n", port)
	fmt.Println()
	
	if err := router.Run(":" + port); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// APIResponse 统一的API响应结构
type APIResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// ProcessImageRequest 处理图片请求的结构体
type ProcessImageRequest struct {
	ImageURL string `json:"image_url" binding:"required"`
}

// setupDemoRoutes 设置演示路由
func setupDemoRoutes(router *gin.Engine) {
	// API版本分组
	v1 := router.Group("/api/v1")
	{
		// 健康检查
		v1.GET("/health", healthCheck)
		
		// 项目信息
		v1.GET("/info", projectInfo)
		
		// 处理图片（演示版本）
		v1.POST("/process-image", processImageDemo)
	}

	// 根路径
	router.GET("/", func(c *gin.Context) {
		c.JSON(http.StatusOK, APIResponse{
			Code:    200,
			Message: "欢迎使用 Go API Solve 演示版本",
			Data: gin.H{
				"version": "1.0.0-demo",
				"endpoints": []string{
					"GET  /api/v1/health",
					"GET  /api/v1/info", 
					"POST /api/v1/process-image",
				},
			},
		})
	})
}

// healthCheck 健康检查
func healthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "服务正常运行",
		Data: gin.H{
			"status":    "healthy",
			"service":   "go-api-solve",
			"version":   "1.0.0-demo",
			"timestamp": time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// projectInfo 项目信息
func projectInfo(c *gin.Context) {
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "项目信息",
		Data: gin.H{
			"name":        "Go API Solve",
			"description": "基于Go+Gin+MySQL+Redis的图片题目解析服务",
			"version":     "1.0.0",
			"features": []string{
				"图片URL验证",
				"Qwen-VL-Plus模型集成",
				"DeepSeek-Chat模型集成",
				"Redis缓存系统",
				"MySQL数据存储",
				"智能题干清洗",
				"多级缓存策略",
				"RESTful API设计",
			},
			"tech_stack": gin.H{
				"language":  "Go 1.21+",
				"framework": "Gin",
				"database":  "MySQL 8.0",
				"cache":     "Redis 6.0",
				"ai_models": []string{"Qwen-VL-Plus", "DeepSeek-Chat"},
			},
		},
	})
}

// processImageDemo 处理图片（演示版本）
func processImageDemo(c *gin.Context) {
	var req ProcessImageRequest
	
	// 绑定请求参数
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "请求参数错误: " + err.Error(),
		})
		return
	}

	// 验证图片URL不为空
	if req.ImageURL == "" {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "图片URL不能为空",
		})
		return
	}

	// 模拟图片URL验证
	if err := utils.ValidateImageURL(req.ImageURL); err != nil {
		c.JSON(http.StatusBadRequest, APIResponse{
			Code:    400,
			Message: "图片资源不存在，请重新上传",
		})
		return
	}

	// 模拟处理延迟
	time.Sleep(1 * time.Second)

	// 生成模拟的响应数据
	responses := generateMockResponse(req.ImageURL)

	// 返回成功响应
	c.JSON(http.StatusOK, APIResponse{
		Code:    200,
		Message: "处理成功（演示模式）",
		Data:    responses,
	})
}

// generateMockResponse 生成模拟响应数据
func generateMockResponse(imageURL string) []model.QuestionResponse {
	// 根据URL生成不同的模拟数据
	userImage := utils.ExtractImageName(imageURL)
	
	// 模拟多选题
	if contains(imageURL, "24") {
		return []model.QuestionResponse{
			{
				QuestionType: "多选题",
				QuestionText: "雾天跟车行驶,应如何安全驾驶?",
				Options: map[string]string{
					"A": "加大跟车距离,降低行驶速度",
					"B": "提前开启雾灯、危险报警闪光灯",
					"C": "以前车尾灯作为判断安全距离的参照物",
					"D": "按喇叭提示行车位置",
				},
				Answer: map[string]string{
					"A": "加大跟车距离,降低行驶速度",
					"B": "提前开启雾灯、危险报警闪光灯",
					"C": "以前车尾灯作为判断安全距离的参照物",
				},
				Analysis:   "在雾天跟车行驶时，首先应该加大跟车距离并降低行驶速度（A选项），这是因为雾天能见度低，需要更多的时间和空间来反应。提前开启雾灯和危险报警闪光灯（B选项）可以增加车辆的可见性，提醒其他车辆注意。以前车尾灯作为判断安全距离的参照物（C选项）是因为在雾天其他参照物可能不清晰，尾灯是一个相对可靠的标志。按喇叭提示行车位置（D选项）在雾天效果有限，因此不是最佳选择。",
				ImageURL:   imageURL,
				UserImage:  userImage,
				IsVerified: "0",
			},
		}
	}
	
	// 模拟判断题
	return []model.QuestionResponse{
		{
			QuestionType: "判断题",
			QuestionText: "驾驶机动车在雾天应使用远光灯提高能见度。",
			Options: map[string]string{
				"Y": "正确",
				"N": "错误",
			},
			Answer: map[string]string{
				"N": "错误",
			},
			Analysis:   "雾天使用远光灯会导致光线反射，降低能见度，正确做法是使用雾灯，因此本题错误。",
			ImageURL:   imageURL,
			UserImage:  userImage,
			IsVerified: "0",
		},
	}
}

// contains 检查字符串是否包含子字符串
func contains(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
