package service

import (
	"context"
	"encoding/json"
	"fmt"
	"go-api-solve/internal/model"
	"go-api-solve/internal/repository"
	"go-api-solve/internal/utils"
	"go-api-solve/pkg/ai"
	"log"
)

// QwenService Qwen服务
type QwenService struct {
	qwenClient           *ai.QwenClient
	modelConfigRepo      *repository.ModelConfigRepository
}

// NewQwenService 创建新的Qwen服务
func NewQwenService(qwenClient *ai.QwenClient, modelConfigRepo *repository.ModelConfigRepository) *QwenService {
	return &QwenService{
		qwenClient:      qwenClient,
		modelConfigRepo: modelConfigRepo,
	}
}

// ProcessImage 处理图片，调用Qwen模型进行识别
func (s *QwenService) ProcessImage(ctx context.Context, imageURL string) (*model.QwenData, string, *model.QwenResponse, error) {
	// 获取Qwen模型配置
	config, err := s.modelConfigRepo.GetByModelName("qwen-vl-plus")
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to get qwen model config: %w", err)
	}

	// 调用Qwen API
	response, err := s.qwenClient.CallQwenVLPlus(ctx, imageURL, config)
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to call qwen API: %w", err)
	}

	// 提取响应内容
	content, err := ai.ExtractContentFromQwenResponse(response)
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to extract content from qwen response: %w", err)
	}

	// 📝 日志1：Qwen返回的原始数据
	log.Printf("=== 📝 日志1：Qwen返回的原始数据 ===")
	log.Printf("Content: %s", content)

	// 🚀 性能优化：只解析一次，不进行完整的FormatQwenData处理
	// 完整的格式化和缓存键生成将在QuestionService中统一处理
	qwenData, err := utils.ParseQuestionJSON(content)
	if err != nil {
		return nil, "", nil, fmt.Errorf("failed to parse qwen data: %w", err)
	}

	// 📝 日志2：第一次解析的结构体
	log.Printf("=== 📝 日志2：第一次解析的结构体 ===")
	qwenDataJSON, _ := json.MarshalIndent(qwenData, "", "  ")
	log.Printf("QwenData: %s", string(qwenDataJSON))

	// 返回解析的数据、原始内容和响应对象
	// content将用于后续的缓存键生成
	return qwenData, content, response, nil
}
