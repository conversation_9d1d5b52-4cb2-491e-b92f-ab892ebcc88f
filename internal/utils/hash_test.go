package utils

import (
	"testing"
)

func TestGenerateCacheKeyFromCleanString(t *testing.T) {
	tests := []struct {
		name           string
		qwenJsonClean  string
		expectedPrefix string
	}{
		{
			name:           "单选题缓存键生成",
			qwenJsonClean:  "如图所示在这个位置时怎样使用灯光A开启左转向灯B开启前照灯C开启危险报警闪光灯D开启右转向灯",
			expectedPrefix: "quest:",
		},
		{
			name:           "判断题缓存键生成",
			qwenJsonClean:  "驾驶未安装制动防抱死装置ABS的机动车在冰雪路面行驶需要制动时应轻踏或间歇踩踏制动踏板N错误Y正确",
			expectedPrefix: "quest:",
		},
		{
			name:           "多选题缓存键生成",
			qwenJsonClean:  "同车道行驶的机动车后车应当与前车保持足以采取紧急制动措施的安全距离下列哪种情形不得超车A前车正在左转弯的B前车正在上下乘客的C前车正在超车的D前车正在掉头的",
			expectedPrefix: "quest:",
		},
		{
			name:           "空字符串",
			qwenJsonClean:  "",
			expectedPrefix: "quest:",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cacheKey := GenerateCacheKeyFromCleanString(tt.qwenJsonClean)
			
			// 验证缓存键格式
			if len(cacheKey) < len(tt.expectedPrefix) {
				t.Errorf("Cache key too short: %s", cacheKey)
				return
			}
			
			if cacheKey[:len(tt.expectedPrefix)] != tt.expectedPrefix {
				t.Errorf("Expected prefix %s, got %s", tt.expectedPrefix, cacheKey[:len(tt.expectedPrefix)])
			}
			
			// 验证哈希部分不为空
			hashPart := cacheKey[len(tt.expectedPrefix):]
			if len(hashPart) == 0 {
				t.Errorf("Hash part is empty")
			}
			
			// 验证相同输入产生相同输出
			cacheKey2 := GenerateCacheKeyFromCleanString(tt.qwenJsonClean)
			if cacheKey != cacheKey2 {
				t.Errorf("Same input should produce same cache key. Got %s and %s", cacheKey, cacheKey2)
			}
		})
	}
}

func TestGenerateCacheKeyConsistency(t *testing.T) {
	// 测试相同内容生成相同缓存键
	content1 := "驾驶未安装制动防抱死装置ABS的机动车在冰雪路面行驶需要制动时应轻踏或间歇踩踏制动踏板N错误Y正确"
	content2 := "驾驶未安装制动防抱死装置ABS的机动车在冰雪路面行驶需要制动时应轻踏或间歇踩踏制动踏板N错误Y正确"
	
	key1 := GenerateCacheKeyFromCleanString(content1)
	key2 := GenerateCacheKeyFromCleanString(content2)
	
	if key1 != key2 {
		t.Errorf("Same content should generate same cache key. Got %s and %s", key1, key2)
	}
	
	// 测试不同内容生成不同缓存键
	content3 := "这是不同的内容"
	key3 := GenerateCacheKeyFromCleanString(content3)
	
	if key1 == key3 {
		t.Errorf("Different content should generate different cache keys. Both got %s", key1)
	}
}

func TestGenerateHashFromString(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected int // 期望的哈希长度（SHA256的十六进制表示应该是64个字符）
	}{
		{
			name:     "正常字符串",
			input:    "test string",
			expected: 64,
		},
		{
			name:     "空字符串",
			input:    "",
			expected: 64,
		},
		{
			name:     "中文字符串",
			input:    "测试中文字符串",
			expected: 64,
		},
		{
			name:     "长字符串",
			input:    "这是一个很长的字符串，用来测试哈希函数是否能正确处理长文本内容，包含各种字符和符号！@#$%^&*()",
			expected: 64,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			hash := GenerateHashFromString(tt.input)
			
			if len(hash) != tt.expected {
				t.Errorf("Expected hash length %d, got %d", tt.expected, len(hash))
			}
			
			// 验证哈希值只包含十六进制字符
			for _, char := range hash {
				if !((char >= '0' && char <= '9') || (char >= 'a' && char <= 'f')) {
					t.Errorf("Hash contains non-hex character: %c", char)
					break
				}
			}
			
			// 验证相同输入产生相同哈希
			hash2 := GenerateHashFromString(tt.input)
			if hash != hash2 {
				t.Errorf("Same input should produce same hash. Got %s and %s", hash, hash2)
			}
		})
	}
}

func TestCacheKeyFormat(t *testing.T) {
	// 测试缓存键格式是否符合 S9.md 要求：quest:哈希值
	testContent := "测试内容"
	cacheKey := GenerateCacheKeyFromCleanString(testContent)
	
	// 验证格式
	expectedPrefix := "quest:"
	if len(cacheKey) <= len(expectedPrefix) {
		t.Errorf("Cache key too short: %s", cacheKey)
		return
	}
	
	if cacheKey[:len(expectedPrefix)] != expectedPrefix {
		t.Errorf("Expected cache key to start with %s, got %s", expectedPrefix, cacheKey)
	}
	
	// 验证哈希部分
	hashPart := cacheKey[len(expectedPrefix):]
	if len(hashPart) != 64 {
		t.Errorf("Expected hash part to be 64 characters, got %d", len(hashPart))
	}
	
	t.Logf("Generated cache key: %s", cacheKey)
	t.Logf("Hash part: %s", hashPart)
}
