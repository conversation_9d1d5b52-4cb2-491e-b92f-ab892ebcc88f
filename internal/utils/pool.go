package utils

import (
	"strings"
	"sync"
)

// 🚀 性能优化：对象池，减少内存分配和GC压力

// StringBuilderPool 字符串构建器对象池
var StringBuilderPool = sync.Pool{
	New: func() interface{} {
		return &strings.Builder{}
	},
}

// GetStringBuilder 从池中获取字符串构建器
func GetStringBuilder() *strings.Builder {
	return StringBuilderPool.Get().(*strings.Builder)
}

// PutStringBuilder 将字符串构建器放回池中
func PutStringBuilder(sb *strings.Builder) {
	sb.Reset()
	StringBuilderPool.Put(sb)
}

// ByteSlicePool 字节切片对象池
var ByteSlicePool = sync.Pool{
	New: func() interface{} {
		// 预分配1KB的缓冲区
		return make([]byte, 0, 1024)
	},
}

// GetByteSlice 从池中获取字节切片
func GetByteSlice() []byte {
	return ByteSlicePool.Get().([]byte)
}

// PutByteSlice 将字节切片放回池中
func PutByteSlice(b []byte) {
	// 重置长度但保留容量
	b = b[:0]
	ByteSlicePool.Put(b)
}

// StringSlicePool 字符串切片对象池
var StringSlicePool = sync.Pool{
	New: func() interface{} {
		// 预分配容量为10的字符串切片
		return make([]string, 0, 10)
	},
}

// GetStringSlice 从池中获取字符串切片
func GetStringSlice() []string {
	return StringSlicePool.Get().([]string)
}

// PutStringSlice 将字符串切片放回池中
func PutStringSlice(s []string) {
	// 重置长度但保留容量
	s = s[:0]
	StringSlicePool.Put(s)
}
