package utils

import (
	"crypto/sha256"
	"encoding/hex"
	"fmt"
)

// 🗑️ 已移除旧的GenerateHash和GenerateCacheKey方法
// 现在统一使用基于qwen_json_clean的新方法

// GenerateCacheKeyFromCleanString 基于qwen_json_clean字符串生成缓存键
// 根据S9.md文档要求：将qwen_json_clean的内容进行哈希化，然后生成缓存键（quest:哈希值）
func GenerateCacheKeyFromCleanString(qwenJsonClean string) string {
	hash := GenerateHashFromString(qwenJsonClean)
	return fmt.Sprintf("quest:%s", hash)
}

// GenerateHashFromString 从字符串生成哈希值
func GenerateHashFromString(data string) string {
	hash := sha256.Sum256([]byte(data))
	return hex.EncodeToString(hash[:])
}
