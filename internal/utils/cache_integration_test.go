package utils

import (
	"testing"
)

func TestCacheKeyGenerationIntegration(t *testing.T) {
	// 测试完整的缓存键生成流程：从Qwen原始数据到缓存键
	tests := []struct {
		name                string
		qwenRawContent      string
		expectedCachePrefix string
	}{
		{
			name: "S8.md文档示例 - 单选题",
			qwenRawContent: `{
				"qutext": "(单选题)20、如图所示，在这个位置时怎样使用灯光？",
				"options": {
					"A": "开启左转向灯",
					"B": "开启前照灯",
					"C": "开启危险报警闪光灯",
					"D": "开启右转向灯"
				}
			}`,
			expectedCachePrefix: "quest:",
		},
		{
			name: "S8.md文档示例 - 多选题",
			qwenRawContent: `{
				"qutext": "(多选题)12、同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
				"options": {
					"A": "前车正在左转弯的",
					"B": "前车正在上下乘客的",
					"C": "前车正在超车的",
					"D": "前车正在掉头的"
				}
			}`,
			expectedCachePrefix: "quest:",
		},
		{
			name: "S8.md文档示例 - 判断题",
			qwenRawContent: `{
				"qutext": "(判断题)4、驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
				"options": {
					"N": "错误",
					"Y": "正确"
				}
			}`,
			expectedCachePrefix: "quest:",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 步骤1：调用FormatQwenData获取qwen_json_clean
			qwenData, qwenJsonClean, err := FormatQwenData(tt.qwenRawContent)
			if err != nil {
				t.Fatalf("FormatQwenData failed: %v", err)
			}

			// 验证qwenData不为空
			if qwenData == nil {
				t.Fatal("qwenData should not be nil")
			}

			// 验证qwenJsonClean不为空
			if qwenJsonClean == "" {
				t.Fatal("qwenJsonClean should not be empty")
			}

			// 步骤2：基于qwen_json_clean生成缓存键
			cacheKey := GenerateCacheKeyFromCleanString(qwenJsonClean)

			// 验证缓存键格式
			if len(cacheKey) <= len(tt.expectedCachePrefix) {
				t.Errorf("Cache key too short: %s", cacheKey)
				return
			}

			if cacheKey[:len(tt.expectedCachePrefix)] != tt.expectedCachePrefix {
				t.Errorf("Expected cache key prefix %s, got %s", tt.expectedCachePrefix, cacheKey[:len(tt.expectedCachePrefix)])
			}

			// 验证哈希部分长度（SHA256十六进制应该是64个字符）
			hashPart := cacheKey[len(tt.expectedCachePrefix):]
			if len(hashPart) != 64 {
				t.Errorf("Expected hash part length 64, got %d", len(hashPart))
			}

			t.Logf("Test: %s", tt.name)
			t.Logf("QwenJsonClean: %s", qwenJsonClean)
			t.Logf("CacheKey: %s", cacheKey)
			t.Logf("HashPart: %s", hashPart)
		})
	}
}

func TestCacheKeyConsistencyAcrossRuns(t *testing.T) {
	// 测试相同输入在多次运行中产生相同的缓存键
	qwenRawContent := `{
		"qutext": "(判断题)4、驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
		"options": {
			"N": "错误",
			"Y": "正确"
		}
	}`

	var cacheKeys []string
	var cleanStrings []string

	// 运行多次
	for i := 0; i < 5; i++ {
		qwenData, qwenJsonClean, err := FormatQwenData(qwenRawContent)
		if err != nil {
			t.Fatalf("Run %d: FormatQwenData failed: %v", i+1, err)
		}

		cacheKey := GenerateCacheKeyFromCleanString(qwenJsonClean)
		
		cacheKeys = append(cacheKeys, cacheKey)
		cleanStrings = append(cleanStrings, qwenJsonClean)

		// 验证基本格式
		if qwenData == nil {
			t.Fatalf("Run %d: qwenData should not be nil", i+1)
		}
		if qwenJsonClean == "" {
			t.Fatalf("Run %d: qwenJsonClean should not be empty", i+1)
		}
		if !startsWith(cacheKey, "quest:") {
			t.Fatalf("Run %d: cache key should start with 'quest:', got %s", i+1, cacheKey)
		}
	}

	// 验证所有运行产生相同的结果
	firstCacheKey := cacheKeys[0]
	firstCleanString := cleanStrings[0]

	for i := 1; i < len(cacheKeys); i++ {
		if cacheKeys[i] != firstCacheKey {
			t.Errorf("Run %d produced different cache key: expected %s, got %s", i+1, firstCacheKey, cacheKeys[i])
		}
		if cleanStrings[i] != firstCleanString {
			t.Errorf("Run %d produced different clean string: expected %s, got %s", i+1, firstCleanString, cleanStrings[i])
		}
	}

	t.Logf("Consistent cache key across %d runs: %s", len(cacheKeys), firstCacheKey)
	t.Logf("Consistent clean string: %s", firstCleanString)
}

func TestDifferentContentProducesDifferentCacheKeys(t *testing.T) {
	// 测试不同内容产生不同的缓存键
	contents := []string{
		`{
			"qutext": "(单选题)1、这是第一个题目",
			"options": {
				"A": "选项A",
				"B": "选项B"
			}
		}`,
		`{
			"qutext": "(单选题)2、这是第二个题目",
			"options": {
				"A": "选项A",
				"B": "选项B"
			}
		}`,
		`{
			"qutext": "(判断题)3、这是判断题",
			"options": {
				"Y": "正确",
				"N": "错误"
			}
		}`,
	}

	var cacheKeys []string

	for i, content := range contents {
		_, qwenJsonClean, err := FormatQwenData(content)
		if err != nil {
			t.Fatalf("Content %d: FormatQwenData failed: %v", i+1, err)
		}

		cacheKey := GenerateCacheKeyFromCleanString(qwenJsonClean)
		cacheKeys = append(cacheKeys, cacheKey)

		t.Logf("Content %d cache key: %s", i+1, cacheKey)
	}

	// 验证所有缓存键都不相同
	for i := 0; i < len(cacheKeys); i++ {
		for j := i + 1; j < len(cacheKeys); j++ {
			if cacheKeys[i] == cacheKeys[j] {
				t.Errorf("Content %d and %d produced same cache key: %s", i+1, j+1, cacheKeys[i])
			}
		}
	}
}

// 辅助函数
func startsWith(s, prefix string) bool {
	return len(s) >= len(prefix) && s[:len(prefix)] == prefix
}
