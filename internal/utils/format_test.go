package utils

import (
	"go-api-solve/internal/model"
	"testing"
)

func TestParseQuestionJSON(t *testing.T) {
	tests := []struct {
		name           string
		input          string
		expectedType   string
		expectedText   string
		expectedOptions map[string]string
		expectError    bool
	}{
		{
			name: "单选题格式",
			input: `{
				"qutext": "(单选题)20、如图所示，在这个位置时怎样使用灯光？",
				"options": {
					"A": "开启左转向灯",
					"B": "开启前照灯",
					"C": "开启危险报警闪光灯",
					"D": "开启右转向灯"
				}
			}`,
			expectedType: "单选题",
			expectedText: "如图所示，在这个位置时怎样使用灯光？",
			expectedOptions: map[string]string{
				"A": "开启左转向灯",
				"B": "开启前照灯",
				"C": "开启危险报警闪光灯",
				"D": "开启右转向灯",
			},
			expectError: false,
		},
		{
			name: "多选题格式",
			input: `{
				"qutext": "(多选题)12、同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
				"options": {
					"A": "前车正在左转弯的",
					"B": "前车正在上下乘客的",
					"C": "前车正在超车的",
					"D": "前车正在掉头的"
				}
			}`,
			expectedType: "多选题",
			expectedText: "同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
			expectedOptions: map[string]string{
				"A": "前车正在左转弯的",
				"B": "前车正在上下乘客的",
				"C": "前车正在超车的",
				"D": "前车正在掉头的",
			},
			expectError: false,
		},
		{
			name: "判断题格式",
			input: `{
				"qutext": "(判断题)4、驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
				"options": {
					"N": "错误",
					"Y": "正确"
				}
			}`,
			expectedType: "判断题",
			expectedText: "驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
			expectedOptions: map[string]string{
				"N": "错误",
				"Y": "正确",
			},
			expectError: false,
		},
		{
			name: "无效题目类型",
			input: `{
				"qutext": "(填空题)1、这是一个填空题",
				"options": {
					"A": "选项A"
				}
			}`,
			expectError: true,
		},
		{
			name: "缺少题目类型标记",
			input: `{
				"qutext": "1、这是一个没有类型标记的题目",
				"options": {
					"A": "选项A"
				}
			}`,
			expectError: true,
		},
		{
			name: "无效JSON格式",
			input: `{invalid json}`,
			expectError: true,
		},
		{
			name: "题目序号使用点号",
			input: `{
				"qutext": "(单选题)1.这是使用点号的题目",
				"options": {
					"A": "选项A"
				}
			}`,
			expectedType: "单选题",
			expectedText: "这是使用点号的题目",
			expectedOptions: map[string]string{
				"A": "选项A",
			},
			expectError: false,
		},
		{
			name: "题目序号使用中文句号",
			input: `{
				"qutext": "(判断题)5。这是使用中文句号的题目",
				"options": {
					"Y": "正确",
					"N": "错误"
				}
			}`,
			expectedType: "判断题",
			expectedText: "这是使用中文句号的题目",
			expectedOptions: map[string]string{
				"Y": "正确",
				"N": "错误",
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := ParseQuestionJSON(tt.input)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if result.QuestionType != tt.expectedType {
				t.Errorf("Expected question type %s, got %s", tt.expectedType, result.QuestionType)
			}

			if result.QuestionText != tt.expectedText {
				t.Errorf("Expected question text %s, got %s", tt.expectedText, result.QuestionText)
			}

			if len(result.Options) != len(tt.expectedOptions) {
				t.Errorf("Expected %d options, got %d", len(tt.expectedOptions), len(result.Options))
			}

			for key, expectedValue := range tt.expectedOptions {
				if actualValue, exists := result.Options[key]; !exists {
					t.Errorf("Expected option %s not found", key)
				} else if actualValue != expectedValue {
					t.Errorf("Expected option %s value %s, got %s", key, expectedValue, actualValue)
				}
			}
		})
	}
}

func TestCleanQuestionFields(t *testing.T) {
	tests := []struct {
		name     string
		input    *model.QwenData
		expected string
	}{
		{
			name: "多选题清洗测试",
			input: &model.QwenData{
				QuestionType: "多选题",
				QuestionText: "同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
				Options: map[string]string{
					"A": "前车正在左转弯的",
					"B": "前车正在上下乘客的",
					"C": "前车正在超车的",
					"D": "前车正在掉头的",
				},
			},
			expected: "同车道行驶的机动车后车应当与前车保持足以采取紧急制动措施的安全距离下列哪种情形不得超车A前车正在左转弯的B前车正在上下乘客的C前车正在超车的D前车正在掉头的",
		},
		{
			name: "判断题清洗测试",
			input: &model.QwenData{
				QuestionType: "判断题",
				QuestionText: "驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
				Options: map[string]string{
					"N": "错误",
					"Y": "正确",
				},
			},
			expected: "驾驶未安装制动防抱死装置ABS的机动车在冰雪路面行驶需要制动时应轻踏或间歇踩踏制动踏板N错误Y正确",
		},
		{
			name: "包含各种符号的清洗测试",
			input: &model.QwenData{
				QuestionType: "单选题",
				QuestionText: "这是一个包含\n换行符\t制表符 空格，标点符号！的题目？",
				Options: map[string]string{
					"A": "选项A：包含冒号",
					"B": "选项B（包含括号）",
				},
			},
			expected: "这是一个包含换行符制表符空格标点符号的题目A选项A包含冒号B选项B包含括号",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := CleanQuestionFields(tt.input)
			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			if result != tt.expected {
				t.Errorf("Expected: %s\nGot: %s", tt.expected, result)
			}
		})
	}
}

func TestFormatQwenData(t *testing.T) {
	tests := []struct {
		name                string
		input               string
		expectedType        string
		expectedText        string
		expectedOptions     map[string]string
		expectedCleanString string
		expectError         bool
	}{
		{
			name: "完整流程测试 - 单选题",
			input: `{
				"qutext": "(单选题)20、如图所示，在这个位置时怎样使用灯光？",
				"options": {
					"A": "开启左转向灯",
					"B": "开启前照灯",
					"C": "开启危险报警闪光灯",
					"D": "开启右转向灯"
				}
			}`,
			expectedType: "单选题",
			expectedText: "如图所示，在这个位置时怎样使用灯光？",
			expectedOptions: map[string]string{
				"A": "开启左转向灯",
				"B": "开启前照灯",
				"C": "开启危险报警闪光灯",
				"D": "开启右转向灯",
			},
			expectedCleanString: "如图所示在这个位置时怎样使用灯光A开启左转向灯B开启前照灯C开启危险报警闪光灯D开启右转向灯",
			expectError:         false,
		},
		{
			name: "完整流程测试 - 判断题",
			input: `{
				"qutext": "(判断题)4、驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
				"options": {
					"N": "错误",
					"Y": "正确"
				}
			}`,
			expectedType: "判断题",
			expectedText: "驾驶未安装制动防抱死装置（ABS）的机动车在冰雪路面行驶，需要制动时，应轻踏或间歇踩踏制动踏板。",
			expectedOptions: map[string]string{
				"N": "错误",
				"Y": "正确",
			},
			expectedCleanString: "驾驶未安装制动防抱死装置ABS的机动车在冰雪路面行驶需要制动时应轻踏或间歇踩踏制动踏板N错误Y正确",
			expectError:         false,
		},
		{
			name: "S8.md文档示例测试 - 多选题",
			input: `{
				"qutext": "(多选题)12、同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
				"options": {
					"A": "前车正在左转弯的",
					"B": "前车正在上下乘客的",
					"C": "前车正在超车的",
					"D": "前车正在掉头的"
				}
			}`,
			expectedType: "多选题",
			expectedText: "同车道行驶的机动车，后车应当与前车保持足以采取紧急制动措施的安全距离。下列哪种情形不得超车？",
			expectedOptions: map[string]string{
				"A": "前车正在左转弯的",
				"B": "前车正在上下乘客的",
				"C": "前车正在超车的",
				"D": "前车正在掉头的",
			},
			expectedCleanString: "同车道行驶的机动车后车应当与前车保持足以采取紧急制动措施的安全距离下列哪种情形不得超车A前车正在左转弯的B前车正在上下乘客的C前车正在超车的D前车正在掉头的",
			expectError:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			qwenData, cleanString, err := FormatQwenData(tt.input)

			if tt.expectError {
				if err == nil {
					t.Errorf("Expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// 验证解析后的数据
			if qwenData.QuestionType != tt.expectedType {
				t.Errorf("Expected question type %s, got %s", tt.expectedType, qwenData.QuestionType)
			}

			if qwenData.QuestionText != tt.expectedText {
				t.Errorf("Expected question text %s, got %s", tt.expectedText, qwenData.QuestionText)
			}

			for key, expectedValue := range tt.expectedOptions {
				if actualValue, exists := qwenData.Options[key]; !exists {
					t.Errorf("Expected option %s not found", key)
				} else if actualValue != expectedValue {
					t.Errorf("Expected option %s value %s, got %s", key, expectedValue, actualValue)
				}
			}

			// 验证清洗后的字符串
			if cleanString != tt.expectedCleanString {
				t.Errorf("Expected clean string: %s\nGot: %s", tt.expectedCleanString, cleanString)
			}
		})
	}
}
