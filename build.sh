#!/bin/bash

# Go API Solve 构建脚本
# 用于构建Linux可执行文件

echo "=== Go API Solve 构建脚本 ==="

# 清理之前的构建
echo "清理之前的构建文件..."
go clean
rm -f go-api-solve-linux*

# 整理依赖
echo "整理Go模块依赖..."
go mod tidy

# 构建标准版本
echo "构建Linux可执行文件（标准版本）..."
GOOS=linux GOARCH=amd64 go build -o go-api-solve-linux cmd/server/main.go

if [ $? -eq 0 ]; then
    echo "✅ 标准版本构建成功: go-api-solve-linux"
    ls -la go-api-solve-linux
else
    echo "❌ 标准版本构建失败"
    exit 1
fi

# 构建优化版本
echo "构建Linux可执行文件（优化版本）..."
GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o go-api-solve-linux-optimized cmd/server/main.go

if [ $? -eq 0 ]; then
    echo "✅ 优化版本构建成功: go-api-solve-linux-optimized"
    ls -la go-api-solve-linux-optimized
else
    echo "❌ 优化版本构建失败"
    exit 1
fi

echo ""
echo "=== 构建完成 ==="
echo "生成的文件："
ls -la go-api-solve-linux*

echo ""
echo "文件信息："
file go-api-solve-linux*

echo ""
echo "推荐使用优化版本: go-api-solve-linux-optimized"
echo "文件大小更小，性能更好"
